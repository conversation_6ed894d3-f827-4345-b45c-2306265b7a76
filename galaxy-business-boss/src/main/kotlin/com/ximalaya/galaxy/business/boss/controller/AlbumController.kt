package com.ximalaya.galaxy.business.boss.controller

import com.ximalaya.common.auth.common.subject.UserContext
import com.ximalaya.galaxy.business.boss.support.buildFailedResult
import com.ximalaya.galaxy.business.business.BizAlbumTrack
import com.ximalaya.galaxy.business.business.boss.BizGalaxyBoss
import com.ximalaya.galaxy.business.business.vo.*
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.repo.dto.GalaxyPage
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

/**
 *<AUTHOR>
 *@create 2025-06-02 10:37
 */
@Api(tags = ["专辑API"])
@RestController
@RequestMapping("/api/album")
class AlbumController(
    private val bizAlbumTrack: BizAlbumTrack,
    private val bizGalaxyBoss: BizGalaxyBoss,
) {

    @ApiOperation(value = "分页获取专辑")
    @GetMapping("/page")
    fun getAlbums(
        @RequestParam("pageNum", required = false) pageNum: Long?,
        @RequestParam("pageSize", required = false) pageSize: Long?
    ): ResultVo<GalaxyPage<AlbumVo?>> {
        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        val pageRequest = QueryPageRequestVo()
        pageNum?.let {
            pageRequest.pageNum = it
        }
        pageSize?.let {
            pageRequest.pageSize = it
        }

        return ResultVo.ok(bizAlbumTrack.getAlbums(uid, pageRequest))
    }


    @ApiOperation(value = "获取专辑和声音")
    @GetMapping("/session/{sessionCode}")
    fun getAlbumBySessionCode(@PathVariable sessionCode: Long): ResultVo<AlbumTrackVo> {
        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        return ResultVo.ok(bizAlbumTrack.getAlbumTrack(uid, sessionCode))
    }

    @ApiOperation(value = "保存(创建或更新)专辑")
    @PostMapping("/session/{sessionCode}/block/{blockCode}")
    fun createAlbum(
        @PathVariable sessionCode: Long,
        @PathVariable blockCode: Long,
        @Valid @RequestBody vo: TmpSaveAlbumVo,
        bindingResult: BindingResult
    ): ResultVo<Unit> {
        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        return when (bindingResult.hasErrors()) {
            true -> bindingResult.buildFailedResult()
            false -> {
                bizAlbumTrack.saveAlbum(uid, sessionCode, blockCode, vo.toSaveAlbumVo()) { id, session ->
                    bizGalaxyBoss.checkSessionUid(id, session)
                }
                ResultVo.ok()
            }
        }
    }

    @ApiOperation(value = "删除专辑")
    @DeleteMapping("/session/{sessionCode}")
    fun removeAlbum(
        @PathVariable sessionCode: Long,
    ): ResultVo<Boolean> {
        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        return ResultVo.ok(bizAlbumTrack.removeAlbum(uid, sessionCode))
    }

}