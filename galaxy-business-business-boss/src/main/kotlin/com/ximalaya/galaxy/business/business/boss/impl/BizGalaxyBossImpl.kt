package com.ximalaya.galaxy.business.business.boss.impl

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.TypeReference
import com.ximalaya.galaxy.business.business.BizAlbumTrack
import com.ximalaya.galaxy.business.business.BusinessComponent
import com.ximalaya.galaxy.business.business.boss.BizGalaxyBoss
import com.ximalaya.galaxy.business.business.boss.BizStreamMessage
import com.ximalaya.galaxy.business.business.boss.BossFootballConfigBean
import com.ximalaya.galaxy.business.business.boss.vo.*
import com.ximalaya.galaxy.business.business.vo.QueryPageRequestVo
import com.ximalaya.galaxy.business.business.vo.ResultVo
import com.ximalaya.galaxy.business.common.ALBUM_OUTLINE
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.dto.GalaxyPage
import com.ximalaya.galaxy.business.repo.entity.AlbumEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxyBlockEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.enums.PhaseState
import com.ximalaya.galaxy.business.repo.service.AlbumService
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import com.ximalaya.galaxy.business.repo.service.GalaxyPhaseService
import com.ximalaya.galaxy.business.repo.service.GalaxySessionService
import com.ximalaya.galaxy.business.service.support.RedisLockHelper
import com.ximalaya.galaxy.business.service.vo.AgentContentPing
import com.ximalaya.galaxy.business.worker.api.converter.CommonJobResponseConverter
import com.ximalaya.galaxy.business.worker.api.thrift.GalaxyBusinessWorkerJobService
import mu.KotlinLogging
import org.apache.commons.lang3.StringUtils
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import java.time.LocalDateTime
import java.util.concurrent.TimeUnit

/**
 *<AUTHOR>
 *@create 2025-05-20 10:54
 */
@BusinessComponent
class BizGalaxyBossImpl(
    private val bizAlbumTrack: BizAlbumTrack,
    private val sessionService: GalaxySessionService,
    private val phaseService: GalaxyPhaseService,
    private val blockService: GalaxyBlockService,
    private val albumService: AlbumService,
    private val workerJobIfaceService: GalaxyBusinessWorkerJobService.Iface,
    private val stringRedisTemplate: StringRedisTemplate,
    private val bizStreamMessage: BizStreamMessage,
    private val lockHelper: RedisLockHelper,
    private val footballConfigBean: BossFootballConfigBean,
) : BizGalaxyBoss {


    @Transactional(rollbackFor = [Throwable::class])
    override fun createSessionAndFirstPhase(
        uid: Long,
        sessionName: String,
        businessDataJson: String?
    ): Pair<String, String> {
        val sessionCode = createSession(uid, sessionName, businessDataJson)
        val phaseCode = createPhase(uid, sessionCode, ALBUM_OUTLINE)
        return Pair(sessionCode.toString(), phaseCode.toString())
    }

    override fun createSession(uid: Long, sessionName: String, businessDataJson: String?): Long {
        val now = LocalDateTime.now()

        val sessionEntity = GalaxySessionEntity().apply {
            this.uid = uid
            this.sessionName = sessionName
            this.businessData = businessDataJson
            this.deletedAt = LogicDeleted.SAVE.getCode()
            this.createTime = now
            this.updateTime = now
        }

        GalaxyAsserts.assertTrue(sessionService.save(sessionEntity), ErrorCode.SESSION_ERROR, "创建会话失败")
        // sessionCode
        return sessionEntity.id!!
    }

    override fun createPhase(uid: Long, sessionCode: Long, phaseName: String): Long {
        val sessionEntity = sessionService.selectBySessionCode(sessionCode)
        GalaxyAsserts.assertNotNull(sessionEntity, ErrorCode.SESSION_ERROR, "会话不存在")
        checkSessionUid(uid, sessionEntity!!)

        val now = LocalDateTime.now()

        return lockHelper.lockAroundAndCheck(
            lockKey = RedisLockHelper.getCreatePhaseLockKey(sessionCode, phaseName),
            leaseTime = 10,
            unit = TimeUnit.SECONDS,
            lockBusyCode = ErrorCode.LOCK_BUSY,
            lockBusyMessage = "正在创建会话中"
        ) {
            val phaseEntity = GalaxyPhaseEntity().apply {
                this.sessionCode = sessionCode
                this.phaseName = phaseName
                this.phaseState = PhaseState.INIT.code
                this.deletedAt = LogicDeleted.SAVE.getCode()
                this.createTime = now
                this.updateTime = now
            }

            GalaxyAsserts.assertTrue(phaseService.save(phaseEntity), ErrorCode.PHASE_ERROR, "创建阶段失败")
            // phaseCode
            phaseEntity.id
        }!!
    }

    override fun getSessionPhases(uid: Long, sessionCode: Long): SessionPhasesVo {
        val sessionEntity = sessionService.selectBySessionCode(sessionCode)
        GalaxyAsserts.assertNotNull(sessionEntity, ErrorCode.SESSION_ERROR, "会话不存在")
        checkSessionUid(uid, sessionEntity!!)

        val phaseEntities = phaseService.selectBySessionCode(sessionCode)
        return SessionPhasesVo.of(sessionEntity, phaseEntities)
    }

    override fun getPhaseCode(uid: Long, sessionCode: Long, phaseName: String): Long? {
        val sessionEntity = sessionService.selectBySessionCode(sessionCode)
        GalaxyAsserts.assertNotNull(sessionEntity, ErrorCode.SESSION_ERROR, "会话不存在")
        checkSessionUid(uid, sessionEntity!!)

        val phase = phaseService.selectBySessionCodeAndPhaseName(sessionCode, phaseName)
        return phase?.phaseCode
    }

    override fun connectSessionPhase(emitter: SseEmitter, uid: Long, sessionCode: Long, phaseCode: Long) {
        // 检查uid是否与sessionCode匹配
        val session = sessionService.selectBySessionCode(sessionCode)
        if (session == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "会话不存在"))
            emitter.complete()
            return
        }

        if (!checkSessionUid(emitter, uid, session)) {
            return
        }

        // 检查phase是否存在
        val phase = phaseService.selectBySessionCodeAndPhaseCode(sessionCode, phaseCode)
        if (phase == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.PHASE_ERROR, "阶段不存在"))
            emitter.complete()
            return
        }

        if (!bizStreamMessage.reconnectStreamChannel(emitter, sessionCode, phaseCode)) {
            return
        }

        if (StringUtils.isBlank(session.businessData)) {
            emitter.send(
                ResultVo.ok(
                    JSON.parseObject(
                        session.businessData,
                        object : TypeReference<Map<String, String>>() {})
                )
            )
        } else {
            emitter.send(ResultVo.ok<Unit>())
        }

        try {
            // 发送一个初始化消息触发历史消息处理
            val initMessage = AgentContentPing().apply {
                this._sessionCode = sessionCode
                this._phaseCode = phaseCode
                this.ts = System.currentTimeMillis()
            }
            bizStreamMessage.sendSseWriteBackMessage(initMessage)
        } catch (e: Exception) {
            logger.error("连接会话失败", e)

            // 连接会话失败
            val pair = if (e is GalaxyException) {
                Pair(ErrorCode.SESSION_ERROR, e.message)
            } else {
                Pair(ErrorCode.CALL_AGENT_ERROR, e.message ?: e.toString())
            }

            val result = ResultVo.failed<Unit>(pair.first, pair.second)
            emitter.send(result)
            emitter.complete()
            bizStreamMessage.releaseStreamChannel(sessionCode, phaseCode)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun startSessionJob(emitter: SseEmitter, startDto: StartSessionJobDto) {
        // 检查uid是否与sessionCode匹配
        val session = sessionService.selectBySessionCode(startDto.sessionCode)
        if (session == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "会话不存在"))
            emitter.complete()
            return
        }

        if (!checkSessionUid(emitter, startDto.uid, session)) {
            return
        }

        // 检查phase是否存在
        val phase = phaseService.selectBySessionCodeAndPhaseCode(startDto.sessionCode, startDto.phaseCode)
        if (phase == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.PHASE_ERROR, "阶段不存在"))
            emitter.complete()
            return
        }

        // 发送worker
        val ongoingRedisKey = RedisLockHelper.getPhaseOngoingLockKey(startDto.sessionCode, startDto.phaseCode)
        // 检查锁是否存在
        if (stringRedisTemplate.hasKey(ongoingRedisKey)) {
            logger.warn("任务重复执行, Session: {}, Phase: {}", startDto.sessionCode, startDto.phaseCode)
            emitter.send(ResultVo.failed<Unit>(ErrorCode.LOCK_BUSY, "重复执行了哦~"))
            emitter.complete()
            return
        }

        if (PhaseState.FAILED.equalsCode(phase.phaseState)) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.PHASE_ERROR, "该会话无法重新启动"))
            emitter.complete()
            return
        }

        if (!bizStreamMessage.connectRealtimeStreamChannel(emitter, startDto.sessionCode, startDto.phaseCode)) {
            return
        }

        if (StringUtils.isBlank(session.businessData)) {
            emitter.send(
                ResultVo.ok(
                    JSON.parseObject(
                        session.businessData,
                        object : TypeReference<Map<String, String>>() {})
                )
            )
        } else {
            emitter.send(ResultVo.ok<Unit>())
        }

        val saveTrackId: Long? = if (phase.phaseName == ALBUM_OUTLINE) {
            null
        } else {
            // 设置声音名称
            val track = bizAlbumTrack.getTrack(startDto.uid, startDto.sessionCode, phase.phaseName!!)
            if (track == null) {
                throw GalaxyException(ErrorCode.SESSION_ERROR, "您还没有保存专辑哦")
            } else {
                track.id
            }
        }

        try {
            val thriftResponse = workerJobIfaceService.startSessionJob(startDto.toStartSessionJobRequest())

            val response = CommonJobResponseConverter.transform(thriftResponse)

            if (response.code != 200) {
                logger.error(
                    "启动任务失败, Session: {}, Phase: {}, Code: {}, Message: {}",
                    startDto.sessionCode,
                    startDto.phaseCode,
                    response.code,
                    response.message
                )

                throw GalaxyException(ErrorCode.SESSION_ERROR, response.message ?: "启动会话失败")
            }

            saveTrackId?.let {
                bizAlbumTrack.saveTrackPhaseCode(it, startDto.phaseCode)
            }

        } catch (e: Exception) {
            // 启动会话失败
            val pair = if (e is GalaxyException) {
                Pair(ErrorCode.SESSION_ERROR, e.message)
            } else {
                Pair(ErrorCode.CALL_AGENT_ERROR, e.message ?: e.toString())
            }

            val result = ResultVo.failed<Unit>(pair.first, pair.second)
            emitter.send(result)
            emitter.complete()
            bizStreamMessage.releaseStreamChannel(startDto.sessionCode, startDto.phaseCode)

            // 任务失败 删除锁
            stringRedisTemplate.delete(ongoingRedisKey)
            return
        }

    }

    override fun startSessionJob(startDto: StartSessionJobWithPhaseNameDto): Boolean {
        // 检查uid是否与sessionCode匹配
        val session = sessionService.selectBySessionCode(startDto.sessionCode)
        GalaxyAsserts.assertNotNull(session, ErrorCode.SESSION_ERROR, "会话不存在")
        checkSessionUid(startDto.uid, session!!)

        // 检查phase是否存在
        val phase = phaseService.selectBySessionCodeAndPhaseName(startDto.sessionCode, startDto.phaseName)
        val phaseCode = if (phase != null) {
            // 新阶段
            phase.phaseCode!!
        } else {
            createPhase(startDto.uid, startDto.sessionCode, startDto.phaseName)
        }

        val saveTrackId: Long? = if (startDto.phaseName == ALBUM_OUTLINE) {
            null
        } else {
            // 设置声音名称
            val track = bizAlbumTrack.getTrack(startDto.uid, startDto.sessionCode, startDto.phaseName)
            if (track == null) {
                throw GalaxyException(ErrorCode.SESSION_ERROR, "您还没有保存专辑哦")
            } else {
                track.id
            }
        }

        val standardDto = StartSessionJobDto(
            startDto.uid,
            startDto.sessionCode,
            phaseCode,
            startDto.systemPrompt,
            startDto.prompt,
        )

        // 发送worker
        val ongoingRedisKey = RedisLockHelper.getPhaseOngoingLockKey(standardDto.sessionCode, phaseCode)
        // 检查锁是否存在
        if (stringRedisTemplate.hasKey(ongoingRedisKey)) {
            logger.warn("任务重复执行, Session: {}, Phase: {}", standardDto.sessionCode, standardDto.phaseCode)
            throw GalaxyException(ErrorCode.LOCK_BUSY, "重复执行了哦~")
        }

        if (PhaseState.FAILED.equalsCode(phase?.phaseState)) {
            throw GalaxyException(ErrorCode.PHASE_ERROR, "该会话无法重新启动")
        }

        try {

            val thriftResponse = workerJobIfaceService.startSessionJob(standardDto.toStartSessionJobRequest())

            val response = CommonJobResponseConverter.transform(thriftResponse)

            if (response.code != 200) {
                logger.error(
                    "启动任务失败, Session: {}, Phase: {}, Code: {}, Message: {}",
                    standardDto.sessionCode,
                    standardDto.phaseCode,
                    response.code,
                    response.message
                )

                throw GalaxyException(ErrorCode.SESSION_ERROR, response.message ?: "启动会话失败")
            }

            saveTrackId?.let {
                bizAlbumTrack.saveTrackPhaseCode(it, phaseCode)
            }

            return true
        } catch (e: Exception) {
            // 启动会话失败
            val pair = if (e is GalaxyException) {
                Pair(ErrorCode.SESSION_ERROR, e.message)
            } else {
                Pair(ErrorCode.CALL_AGENT_ERROR, e.message ?: e.toString())
            }

            // 任务失败 删除锁
            stringRedisTemplate.delete(ongoingRedisKey)

            throw GalaxyException(pair.first, pair.second)
        }
    }


    override fun startToolJob(emitter: SseEmitter, startDto: StartToolJobDto) {
        // 检查uid是否与sessionCode匹配
        val session = sessionService.selectBySessionCode(startDto.sessionCode)
        if (session == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "会话不存在"))
            emitter.complete()
            return
        }

        if (!checkSessionUid(emitter, startDto.uid, session)) {
            return
        }

        // 检查phase是否存在
        val phase = phaseService.selectBySessionCodeAndPhaseCode(startDto.sessionCode, startDto.phaseCode)
        if (phase == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.PHASE_ERROR, "阶段不存在"))
            emitter.complete()
            return
        }

        // 发送worker
        val ongoingRedisKey = RedisLockHelper.getPhaseOngoingLockKey(startDto.sessionCode, startDto.phaseCode)
        // 检查锁是否存在
        if (stringRedisTemplate.hasKey(ongoingRedisKey)) {
            logger.warn("任务重复执行, Session: {}, Phase: {}", startDto.sessionCode, startDto.phaseCode)
            emitter.send(ResultVo.failed<Unit>(ErrorCode.LOCK_BUSY, "重复执行了哦~"))
            emitter.complete()
            return
        }

        if (PhaseState.FAILED.equalsCode(phase.phaseState)) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.PHASE_ERROR, "该会话无法重新启动"))
            emitter.complete()
            return
        }

        if (!bizStreamMessage.connectRealtimeStreamChannel(emitter, startDto.sessionCode, startDto.phaseCode)) {
            return
        }

        val saveTrackId: Long? = if (phase.phaseName == ALBUM_OUTLINE) {
            null
        } else {
            // 设置声音名称
            val track = bizAlbumTrack.getTrack(startDto.uid, startDto.sessionCode, phase.phaseName!!)
            if (track == null) {
                throw GalaxyException(ErrorCode.SESSION_ERROR, "您还没有保存专辑哦")
            } else {
                track.id
            }
        }

        if (StringUtils.isBlank(session.businessData)) {
            emitter.send(
                ResultVo.ok(
                    JSON.parseObject(
                        session.businessData,
                        object : TypeReference<Map<String, String>>() {})
                )
            )
        } else {
            emitter.send(ResultVo.ok<Unit>())
        }

        try {
            val thriftResponse = workerJobIfaceService.startToolJob(startDto.toStartToolJobRequest())

            val response = CommonJobResponseConverter.transform(thriftResponse)

            if (response.code != 200) {
                logger.error(
                    "启动工具失败, Session: {}, Phase: {}, Code: {}, Message: {}",
                    startDto.sessionCode,
                    startDto.phaseCode,
                    response.code,
                    response.message
                )

                throw GalaxyException(ErrorCode.SESSION_ERROR, response.message ?: "启动会话失败")
            }

            saveTrackId?.let {
                bizAlbumTrack.saveTrackPhaseCode(it, startDto.phaseCode)
            }

        } catch (e: Exception) {
            // 启动工具失败
            val pair = if (e is GalaxyException) {
                Pair(ErrorCode.SESSION_ERROR, e.message)
            } else {
                Pair(ErrorCode.CALL_AGENT_ERROR, e.message ?: e.toString())
            }

            val result = ResultVo.failed<Unit>(pair.first, pair.second)
            emitter.send(result)
            emitter.complete()
            bizStreamMessage.releaseStreamChannel(startDto.sessionCode, startDto.phaseCode)

            // 任务失败 删除锁
            stringRedisTemplate.delete(ongoingRedisKey)
            return
        }
    }

    override fun startToolJob(startDto: StartToolJobWithPhaseNameDto): Boolean {
        // 检查uid是否与sessionCode匹配
        val session = sessionService.selectBySessionCode(startDto.sessionCode)
        GalaxyAsserts.assertNotNull(session, ErrorCode.SESSION_ERROR, "会话不存在")
        checkSessionUid(startDto.uid, session!!)

        // 检查phase是否存在
        val phase = phaseService.selectBySessionCodeAndPhaseName(startDto.sessionCode, startDto.phaseName)
        val phaseCode = if (phase != null) {
            // 新阶段
            phase.phaseCode!!
        } else {
            createPhase(startDto.uid, startDto.sessionCode, startDto.phaseName)
        }

        val saveTrackId: Long? = if (startDto.phaseName == ALBUM_OUTLINE) {
            null
        } else {
            // 设置声音名称
            val track = bizAlbumTrack.getTrack(startDto.uid, startDto.sessionCode, startDto.phaseName)
            if (track == null) {
                throw GalaxyException(ErrorCode.SESSION_ERROR, "您还没有保存专辑哦")
            } else {
                track.id
            }
        }

        val standardDto = StartToolJobDto(
            startDto.uid,
            startDto.sessionCode,
            phaseCode,
            startDto.toolId,
            startDto.args,
            startDto.needRemoveBlockCodes,
        )

        // 发送worker
        val ongoingRedisKey = RedisLockHelper.getPhaseOngoingLockKey(standardDto.sessionCode, phaseCode)
        // 检查锁是否存在
        if (stringRedisTemplate.hasKey(ongoingRedisKey)) {
            logger.warn("任务重复执行, Session: {}, Phase: {}", standardDto.sessionCode, standardDto.phaseCode)
            throw GalaxyException(ErrorCode.LOCK_BUSY, "重复执行了哦~")
        }

        if (PhaseState.FAILED.equalsCode(phase?.phaseState)) {
            throw GalaxyException(ErrorCode.PHASE_ERROR, "该会话无法重新启动")
        }

        try {

            val thriftResponse = workerJobIfaceService.startToolJob(standardDto.toStartToolJobRequest())

            val response = CommonJobResponseConverter.transform(thriftResponse)

            if (response.code != 200) {
                logger.error(
                    "启动工具失败, Session: {}, Phase: {}, Code: {}, Message: {}",
                    standardDto.sessionCode,
                    standardDto.phaseCode,
                    response.code,
                    response.message
                )

                throw GalaxyException(ErrorCode.SESSION_ERROR, response.message ?: "启动会话失败")
            }

            saveTrackId?.let {
                bizAlbumTrack.saveTrackPhaseCode(it, phaseCode)
            }

            return true
        } catch (e: Exception) {
            // 启动工具失败
            val pair = if (e is GalaxyException) {
                Pair(ErrorCode.SESSION_ERROR, e.message)
            } else {
                Pair(ErrorCode.CALL_AGENT_ERROR, e.message ?: e.toString())
            }

            // 任务失败 删除锁
            stringRedisTemplate.delete(ongoingRedisKey)

            throw GalaxyException(pair.first, pair.second)
        }
    }

    override fun updateBlockContent(sessionCode: Long, phaseCode: Long, blockCode: Long, content: String): Boolean {
        val ongoingRedisKey = RedisLockHelper.getPhaseOngoingLockKey(sessionCode, phaseCode)
        // 检查锁是否存在
        GalaxyAsserts.assertFalse(
            stringRedisTemplate.hasKey(ongoingRedisKey),
            ErrorCode.BLOCK_ERROR,
            "正在执行中请稍后哦~"
        )

        return blockService.updateBlock(sessionCode, phaseCode, blockCode, content)
    }

    override fun getHistoricalSessions(uid: Long, pageRequest: QueryPageRequestVo): GalaxyPage<SessionVo> {
        val page = sessionService.ktQuery()
            .eq(GalaxySessionEntity::uid, uid)
            .eq(GalaxySessionEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .orderByDesc(GalaxySessionEntity::createTime)
            .page(pageRequest.toPageInfo())

        return GalaxyPage.of(page).map { SessionVo.of(it) }
    }

    @Transactional(rollbackFor = [Throwable::class])
    override fun removeSession(uid: Long, sessionCode: Long): Boolean {
        // 必须先删专辑
        val album = albumService.ktQuery()
            .eq(AlbumEntity::sessionCode, sessionCode)
            .eq(AlbumEntity::uid, uid)
            .eq(AlbumEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .one()
        GalaxyAsserts.assertNull(album, ErrorCode.ALBUM_ERROR, "必须删除专辑后才能删除会话哦~")

        val session = sessionService.selectBySessionCode(sessionCode) ?: return true
        GalaxyAsserts.assertEquals(uid, session.uid, ErrorCode.SESSION_ERROR, "这不是您的会话哦~")

        val now = LocalDateTime.now()

        GalaxyAsserts.assertTrue(
            sessionService.ktUpdate()
                .eq(GalaxySessionEntity::sessionCode, sessionCode)
                .set(GalaxySessionEntity::deletedAt, LogicDeleted.DELETED.getCode())
                .set(GalaxySessionEntity::updateTime, now)
                .update(), ErrorCode.SESSION_ERROR, "删除会话失败"
        )

        GalaxyAsserts.assertTrue(
            phaseService.ktUpdate()
                .eq(GalaxyPhaseEntity::sessionCode, sessionCode)
                .set(GalaxyPhaseEntity::deletedAt, LogicDeleted.DELETED.getCode())
                .set(GalaxyPhaseEntity::updateTime, now)
                .update(), ErrorCode.SESSION_ERROR, "删除阶段失败"
        )

        GalaxyAsserts.assertTrue(
            blockService.ktUpdate()
                .eq(GalaxyBlockEntity::sessionCode, sessionCode)
                .set(GalaxyBlockEntity::deletedAt, LogicDeleted.DELETED.getCode())
                .set(GalaxyBlockEntity::updateTime, now)
                .update(), ErrorCode.SESSION_ERROR, "删除数据失败"
        )

        return true
    }

    override fun checkSessionUid(uid: Long, session: GalaxySessionEntity) {
        // 管理员不判断
        if (!footballConfigBean.isAdmin(uid)) {
            GalaxyAsserts.assertEquals(session.uid, uid, ErrorCode.SESSION_ERROR, "这里不是您的会话哦~")
        }
    }

    override fun checkSessionUid(emitter: SseEmitter, uid: Long, session: GalaxySessionEntity): Boolean {
        // 管理员不判断
        if (footballConfigBean.isAdmin(uid)) {
            return true
        }

        if (session.uid != uid) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "这里不是您的会话哦~"))
            emitter.complete()
            return false
        }

        return true
    }

    companion object {

        private val logger = KotlinLogging.logger { }

    }

}